[{"E:\\indicator-qa-service\\datafront\\src\\main.js": "1", "E:\\indicator-qa-service\\datafront\\src\\App.vue": "2", "E:\\indicator-qa-service\\datafront\\src\\components\\ChartDisplay.vue": "3", "E:\\indicator-qa-service\\datafront\\src\\api\\index.js": "4", "E:\\frontCodeCode\\datafront\\src\\main.js": "5", "E:\\frontCodeCode\\datafront\\src\\App.vue": "6", "E:\\frontCodeCode\\datafront\\src\\components\\ChartDisplay.vue": "7", "E:\\frontCodeCode\\datafront\\src\\api\\index.js": "8"}, {"size": 343, "mtime": 1751352483989, "results": "9", "hashOfConfig": "10"}, {"size": 140569, "mtime": 1753756203879, "results": "11", "hashOfConfig": "10"}, {"size": 15947, "mtime": 1753260542700, "results": "12", "hashOfConfig": "10"}, {"size": 3817, "mtime": 1752743839756, "results": "13", "hashOfConfig": "10"}, {"size": 343, "mtime": 1751352483989, "results": "14", "hashOfConfig": "15"}, {"size": 139548, "mtime": 1753346493065, "results": "16", "hashOfConfig": "15"}, {"size": 15947, "mtime": 1753260542700, "results": "17", "hashOfConfig": "15"}, {"size": 3817, "mtime": 1752743839756, "results": "18", "hashOfConfig": "15"}, {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "21"}, "n5xxqj", {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "21"}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "30"}, "q1zo4s", {"filePath": "31", "messages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "30"}, "E:\\indicator-qa-service\\datafront\\src\\main.js", [], [], "E:\\indicator-qa-service\\datafront\\src\\App.vue", [], "E:\\indicator-qa-service\\datafront\\src\\components\\ChartDisplay.vue", [], "E:\\indicator-qa-service\\datafront\\src\\api\\index.js", [], "E:\\frontCodeCode\\datafront\\src\\main.js", [], [], "E:\\frontCodeCode\\datafront\\src\\App.vue", [], "E:\\frontCodeCode\\datafront\\src\\components\\ChartDisplay.vue", [], [], "E:\\frontCodeCode\\datafront\\src\\api\\index.js", []]